#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Vditor Backend Service 启动器\n');

// 检查Node.js版本
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
  console.error('❌ 需要Node.js 16或更高版本');
  console.error(`   当前版本: ${nodeVersion}`);
  console.error('   请升级Node.js: https://nodejs.org/');
  process.exit(1);
}

console.log(`✅ Node.js版本检查通过: ${nodeVersion}`);

// 检查package.json是否存在
const packageJsonPath = path.join(__dirname, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ 未找到package.json文件');
  console.error('   请确保在backend目录中运行此脚本');
  process.exit(1);
}

console.log('✅ package.json文件存在');

// 检查node_modules是否存在
const nodeModulesPath = path.join(__dirname, 'node_modules');
const needInstall = !fs.existsSync(nodeModulesPath);

if (needInstall) {
  console.log('📦 检测到需要安装依赖...');
  console.log('   正在运行: npm install');
  
  const installProcess = spawn('npm', ['install'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true
  });

  installProcess.on('close', (code) => {
    if (code !== 0) {
      console.error('❌ 依赖安装失败');
      process.exit(1);
    }
    
    console.log('✅ 依赖安装完成');
    startServer();
  });

  installProcess.on('error', (error) => {
    console.error('❌ 启动npm install失败:', error.message);
    console.error('   请手动运行: npm install');
    process.exit(1);
  });
} else {
  console.log('✅ 依赖已安装');
  startServer();
}

function startServer() {
  console.log('\n🎯 启动服务器...');
  
  // 设置环境变量
  const env = {
    ...process.env,
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: process.env.PORT || '3001'
  };

  // 启动服务器
  const serverProcess = spawn('node', ['server.js'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true,
    env: env
  });

  // 处理进程退出
  serverProcess.on('close', (code) => {
    if (code !== 0) {
      console.error(`❌ 服务器异常退出，退出码: ${code}`);
    } else {
      console.log('✅ 服务器正常关闭');
    }
  });

  serverProcess.on('error', (error) => {
    console.error('❌ 启动服务器失败:', error.message);
    process.exit(1);
  });

  // 优雅关闭处理
  process.on('SIGINT', () => {
    console.log('\n🛑 收到中断信号，正在关闭服务器...');
    serverProcess.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 收到终止信号，正在关闭服务器...');
    serverProcess.kill('SIGTERM');
  });
}

# Vditor后端服务使用指南

## 📋 概述

这个Node.js后端服务封装了vditor的`getHTML()`方法，让您可以在服务器端将Markdown内容转换为HTML，完全兼容前端vditor的所有功能。

## 🚀 快速开始

### 1. 启动后端服务

```bash
# 进入后端目录
cd backend

# 自动安装依赖并启动服务
node start.js

# 或者手动安装依赖
npm install
npm start
```

服务将在 `http://localhost:3001` 启动

### 2. 在前端中使用

```typescript
import { vditorService } from '@/utils/vditorService';

// 转换Markdown为HTML
const html = await vditorService.convert(markdownContent);
console.log(html);
```

### 3. 使用转换器组件

```vue
<template>
  <VditorConverter />
</template>

<script setup>
import VditorConverter from '@/components/VditorConverter.vue';
</script>
```

## 🔧 API接口

### 主要转换接口

| 接口 | 方法 | 描述 | 速度 | 兼容性 |
|------|------|------|------|--------|
| `/api/vditor/convert` | POST | 使用vditor引擎 | 慢 (1-3s) | 100% |
| `/api/markdown/convert` | POST | 使用markdown-it | 快 (<100ms) | 80% |

### 辅助接口

- `GET /api/health` - 健康检查
- `GET /api/methods` - 获取支持的方法

## 💡 使用建议

### 选择转换方法

1. **推荐使用vditor方法**：
   - 完全兼容前端vditor配置
   - 支持所有Markdown扩展语法
   - 数学公式、代码高亮等功能完整

2. **轻量级场景使用markdown-it**：
   - 转换速度快
   - 资源占用少
   - 适合简单Markdown内容

3. **自动选择模式**：
   - 优先尝试vditor
   - 失败时自动回退到markdown-it

### 性能优化

```typescript
// 批量转换
const htmlResults = await vditorService.batchConvert([
  'markdown1',
  'markdown2', 
  'markdown3'
]);

// 设置超时时间
vditorService.setTimeout(10000); // 10秒

// 健康检查
const health = await vditorService.checkHealth();
```

## 🛠️ 配置选项

### Vditor配置

```typescript
const options = {
  mode: 'ir',           // 编辑模式: ir, wysiwyg, sv
  height: 300,          // 编辑器高度
  theme: 'classic'      // 主题
};

const html = await vditorService.convertWithVditor(content, options);
```

### 环境变量

```bash
PORT=3001                    # 服务端口
NODE_ENV=production         # 运行环境
PUPPETEER_HEADLESS=new      # Puppeteer模式
```

## 🔍 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查Node.js版本 (需要>=16)
   node --version
   
   # 检查端口占用
   netstat -ano | findstr :3001
   ```

2. **Puppeteer安装问题**
   ```bash
   # 手动安装Chromium
   npx puppeteer browsers install chrome
   ```

3. **内存不足**
   ```bash
   # 增加内存限制
   node --max-old-space-size=4096 server.js
   ```

### 错误处理

```typescript
try {
  const html = await vditorService.convert(content);
} catch (error) {
  console.error('转换失败:', error.message);
  // 处理错误逻辑
}
```

## 📊 性能对比

| 指标 | Vditor方法 | Markdown-it方法 |
|------|------------|-----------------|
| 转换速度 | 1-3秒 | <100ms |
| 内存占用 | 高 (~100MB) | 低 (~10MB) |
| 功能完整性 | 100% | 80% |
| 数学公式 | ✅ | ✅ |
| 代码高亮 | ✅ | ✅ |
| 自定义渲染 | ✅ | ❌ |

## 🚀 部署建议

### Docker部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY backend/package*.json ./
RUN npm ci --only=production
COPY backend/ .
EXPOSE 3001
CMD ["npm", "start"]
```

### PM2部署

```bash
npm install -g pm2
cd backend
pm2 start server.js --name vditor-service
pm2 startup
pm2 save
```

## 📝 示例代码

### 基础使用

```typescript
import { vditorService } from '@/utils/vditorService';

// 简单转换
const html = await vditorService.convert('# Hello World');

// 指定方法转换
const html1 = await vditorService.convertWithVditor(content);
const html2 = await vditorService.convertWithMarkdownIt(content);
```

### Vue组件中使用

```vue
<script setup>
import { ref } from 'vue';
import { vditorService } from '@/utils/vditorService';

const content = ref('');
const html = ref('');

const convert = async () => {
  try {
    html.value = await vditorService.convert(content.value);
  } catch (error) {
    console.error('转换失败:', error);
  }
};
</script>
```

## 🔗 相关链接

- [Vditor官方文档](https://github.com/Vanessa219/vditor)
- [Puppeteer文档](https://pptr.dev/)
- [Markdown-it文档](https://github.com/markdown-it/markdown-it)

## 📞 技术支持

如果遇到问题，请检查：

1. ✅ Node.js版本 >= 16
2. ✅ 服务是否正常启动
3. ✅ 网络连接是否正常
4. ✅ 防火墙设置是否正确

---

**注意**: 这个服务主要用于开发和测试环境。生产环境部署时请考虑负载均衡、监控和安全性配置。

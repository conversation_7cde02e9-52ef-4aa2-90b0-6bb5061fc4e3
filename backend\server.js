const express = require('express');
const cors = require('cors');
const puppeteer = require('puppeteer');
const MarkdownIt = require('markdown-it');
const markdownItKatex = require('@iktakahiro/markdown-it-katex');
const hljs = require('highlight.js');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// 配置markdown-it（备选方案）
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value;
      } catch (__) {}
    }
    return '';
  }
}).use(markdownItKatex);

// 全局浏览器实例
let browser = null;

// 初始化浏览器
async function initBrowser() {
  if (!browser) {
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
  }
  return browser;
}

// 创建vditor HTML模板
const createVditorHTML = (content, options = {}) => {
  const {
    mode = 'ir',
    height = 300,
    theme = 'classic'
  } = options;

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Vditor HTML Converter</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vditor@3.11.0/dist/index.css" />
    <script src="https://cdn.jsdelivr.net/npm/vditor@3.11.0/dist/index.min.js"></script>
</head>
<body>
    <div id="vditor"></div>
    <script>
        window.vditorInstance = null;
        window.initVditor = function(content, options) {
            return new Promise((resolve, reject) => {
                try {
                    window.vditorInstance = new Vditor('vditor', {
                        height: ${height},
                        mode: '${mode}',
                        cache: { enable: false },
                        preview: {
                            markdown: {
                                autoSpace: true,
                                fixTermTypo: true,
                                paragraphBeginningSpace: true,
                                sanitize: true,
                                listStyle: true,
                                mark: true
                            },
                            hljs: {
                                style: 'monokai'
                            },
                            math: {
                                engine: 'KaTeX'
                            }
                        },
                        after: () => {
                            window.vditorInstance.setValue(content);
                            resolve();
                        }
                    });
                } catch (error) {
                    reject(error);
                }
            });
        };
        
        window.getHTML = function() {
            if (window.vditorInstance) {
                return window.vditorInstance.getHTML();
            }
            return '';
        };
    </script>
</body>
</html>`;
};

// API路由：使用vditor转换（方案一）
app.post('/api/vditor/convert', async (req, res) => {
  try {
    const { content, options = {} } = req.body;
    
    if (!content) {
      return res.status(400).json({ error: '内容不能为空' });
    }

    // 初始化浏览器
    await initBrowser();
    const page = await browser.newPage();
    
    try {
      // 设置HTML内容
      const html = createVditorHTML(content, options);
      await page.setContent(html);
      
      // 等待vditor加载完成
      await page.waitForFunction(() => window.Vditor !== undefined, { timeout: 10000 });
      
      // 初始化vditor并获取HTML
      await page.evaluate((content, options) => {
        return window.initVditor(content, options);
      }, content, options);
      
      // 等待一段时间确保内容处理完成
      await page.waitForTimeout(1000);
      
      // 获取HTML结果
      const htmlResult = await page.evaluate(() => {
        return window.getHTML();
      });
      
      res.json({
        success: true,
        html: htmlResult,
        method: 'vditor'
      });
      
    } finally {
      await page.close();
    }
    
  } catch (error) {
    console.error('Vditor转换错误:', error);
    res.status(500).json({ 
      error: '转换失败', 
      details: error.message 
    });
  }
});

// API路由：使用markdown-it转换（方案二，备选）
app.post('/api/markdown/convert', async (req, res) => {
  try {
    const { content, options = {} } = req.body;
    
    if (!content) {
      return res.status(400).json({ error: '内容不能为空' });
    }

    // 使用markdown-it转换
    const html = md.render(content);
    
    res.json({
      success: true,
      html: html,
      method: 'markdown-it'
    });
    
  } catch (error) {
    console.error('Markdown-it转换错误:', error);
    res.status(500).json({ 
      error: '转换失败', 
      details: error.message 
    });
  }
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    browser: browser ? 'connected' : 'disconnected'
  });
});

// 获取支持的转换方法
app.get('/api/methods', (req, res) => {
  res.json({
    methods: [
      {
        name: 'vditor',
        endpoint: '/api/vditor/convert',
        description: '使用vditor引擎转换，完全兼容前端vditor配置'
      },
      {
        name: 'markdown-it',
        endpoint: '/api/markdown/convert', 
        description: '使用markdown-it引擎转换，轻量级方案'
      }
    ]
  });
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('正在关闭服务器...');
  if (browser) {
    await browser.close();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('正在关闭服务器...');
  if (browser) {
    await browser.close();
  }
  process.exit(0);
});

app.listen(PORT, () => {
  console.log(`🚀 Node.js后端服务已启动`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔧 API端点:`);
  console.log(`   - POST /api/vditor/convert - 使用vditor转换`);
  console.log(`   - POST /api/markdown/convert - 使用markdown-it转换`);
  console.log(`   - GET /api/health - 健康检查`);
  console.log(`   - GET /api/methods - 获取支持的方法`);
});

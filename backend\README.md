# Vditor Backend Service

一个Node.js后端服务，封装vditor的getHTML()方法，提供Markdown到HTML的转换功能。

## 功能特性

- 🚀 **完全兼容vditor**: 使用Puppeteer在无头浏览器中运行真实的vditor实例
- ⚡ **轻量级备选方案**: 提供基于markdown-it的快速转换
- 🔧 **灵活配置**: 支持vditor的各种配置选项
- 🛡️ **安全可靠**: 内置XSS防护和内容清理
- 📊 **性能监控**: 提供健康检查和性能指标

## 安装和启动

### 1. 安装依赖

```bash
cd backend
npm install
```

### 2. 启动服务

```bash
# 生产环境
npm start

# 开发环境（自动重启）
npm run dev
```

服务将在 `http://localhost:3001` 启动

### 3. 运行测试

```bash
npm test
```

## API接口

### 1. Vditor转换 (推荐)

**POST** `/api/vditor/convert`

使用真实的vditor实例进行转换，完全兼容前端vditor的所有功能。

```javascript
// 请求
{
  "content": "# 标题\n\n这是**粗体**文本",
  "options": {
    "mode": "ir",        // 编辑模式: ir, wysiwyg, sv
    "height": 300,       // 编辑器高度
    "theme": "classic"   // 主题
  }
}

// 响应
{
  "success": true,
  "html": "<h1>标题</h1><p>这是<strong>粗体</strong>文本</p>",
  "method": "vditor"
}
```

### 2. Markdown-it转换 (轻量级)

**POST** `/api/markdown/convert`

使用markdown-it库进行快速转换，适合简单场景。

```javascript
// 请求
{
  "content": "# 标题\n\n这是**粗体**文本"
}

// 响应
{
  "success": true,
  "html": "<h1>标题</h1><p>这是<strong>粗体</strong>文本</p>",
  "method": "markdown-it"
}
```

### 3. 健康检查

**GET** `/api/health`

```javascript
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "browser": "connected"
}
```

### 4. 获取支持的方法

**GET** `/api/methods`

```javascript
{
  "methods": [
    {
      "name": "vditor",
      "endpoint": "/api/vditor/convert",
      "description": "使用vditor引擎转换，完全兼容前端vditor配置"
    },
    {
      "name": "markdown-it", 
      "endpoint": "/api/markdown/convert",
      "description": "使用markdown-it引擎转换，轻量级方案"
    }
  ]
}
```

## 在前端中使用

### Vue.js示例

```javascript
// 封装API调用
async function convertMarkdownToHTML(content, useVditor = true) {
  const endpoint = useVditor ? '/api/vditor/convert' : '/api/markdown/convert';
  
  try {
    const response = await fetch(`http://localhost:3001${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: content,
        options: {
          mode: 'ir',
          height: 300
        }
      })
    });
    
    const result = await response.json();
    return result.html;
  } catch (error) {
    console.error('转换失败:', error);
    return '';
  }
}

// 在组件中使用
export default {
  methods: {
    async handleConvert() {
      const markdownContent = this.vditorInstance.getValue();
      const html = await convertMarkdownToHTML(markdownContent);
      console.log('转换结果:', html);
    }
  }
}
```

### JavaScript/TypeScript示例

```typescript
interface ConvertOptions {
  mode?: 'ir' | 'wysiwyg' | 'sv';
  height?: number;
  theme?: string;
}

class VditorService {
  private baseURL = 'http://localhost:3001';

  async convertToHTML(content: string, options?: ConvertOptions): Promise<string> {
    const response = await fetch(`${this.baseURL}/api/vditor/convert`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ content, options })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error);
    }

    return result.html;
  }
}

// 使用示例
const vditorService = new VditorService();
const html = await vditorService.convertToHTML('# Hello World');
```

## 性能对比

| 方法 | 速度 | 兼容性 | 功能完整性 | 资源占用 |
|------|------|--------|------------|----------|
| Vditor | 较慢 (1-3s) | 100% | 完整 | 高 |
| Markdown-it | 快速 (<100ms) | 80% | 基础 | 低 |

## 配置选项

### Vditor配置

支持所有vditor的配置选项，包括：

- `mode`: 编辑模式 (ir, wysiwyg, sv)
- `height`: 编辑器高度
- `preview.markdown`: Markdown渲染配置
- `preview.hljs`: 代码高亮配置
- `preview.math`: 数学公式配置

### 环境变量

```bash
PORT=3001                    # 服务端口
NODE_ENV=production         # 运行环境
PUPPETEER_HEADLESS=new      # Puppeteer模式
```

## 故障排除

### 1. Puppeteer安装问题

```bash
# 手动安装Chromium
npx puppeteer browsers install chrome
```

### 2. 内存不足

```bash
# 增加Node.js内存限制
node --max-old-space-size=4096 server.js
```

### 3. 端口占用

```bash
# 检查端口占用
netstat -ano | findstr :3001

# 修改端口
PORT=3002 npm start
```

## 部署建议

### Docker部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

### PM2部署

```bash
npm install -g pm2
pm2 start server.js --name vditor-service
pm2 startup
pm2 save
```

## 许可证

MIT License

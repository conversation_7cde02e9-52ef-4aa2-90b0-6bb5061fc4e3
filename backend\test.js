const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

// 测试用的Markdown内容
const testMarkdown = `# 测试标题

这是一个**粗体**文本和*斜体*文本的示例。

## 代码块测试

\`\`\`javascript
function hello() {
    console.log("Hello, World!");
}
\`\`\`

## 列表测试

- 项目1
- 项目2
  - 子项目1
  - 子项目2

## 数学公式测试

行内公式：$E = mc^2$

块级公式：
$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$

## 表格测试

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

## 链接测试

[这是一个链接](https://example.com)

> 这是一个引用块
> 可以包含多行内容
`;

async function testAPI() {
  console.log('🧪 开始测试Node.js后端服务...\n');

  try {
    // 1. 健康检查
    console.log('1️⃣ 测试健康检查...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ 健康检查通过:', healthResponse.data);
    console.log('');

    // 2. 获取支持的方法
    console.log('2️⃣ 获取支持的转换方法...');
    const methodsResponse = await axios.get(`${BASE_URL}/api/methods`);
    console.log('✅ 支持的方法:', methodsResponse.data);
    console.log('');

    // 3. 测试vditor转换
    console.log('3️⃣ 测试vditor转换...');
    const vditorResponse = await axios.post(`${BASE_URL}/api/vditor/convert`, {
      content: testMarkdown,
      options: {
        mode: 'ir',
        height: 300
      }
    });
    console.log('✅ Vditor转换成功');
    console.log('📄 HTML长度:', vditorResponse.data.html.length);
    console.log('🔧 使用方法:', vditorResponse.data.method);
    console.log('📝 HTML预览 (前200字符):', vditorResponse.data.html.substring(0, 200) + '...');
    console.log('');

    // 4. 测试markdown-it转换
    console.log('4️⃣ 测试markdown-it转换...');
    const markdownResponse = await axios.post(`${BASE_URL}/api/markdown/convert`, {
      content: testMarkdown
    });
    console.log('✅ Markdown-it转换成功');
    console.log('📄 HTML长度:', markdownResponse.data.html.length);
    console.log('🔧 使用方法:', markdownResponse.data.method);
    console.log('📝 HTML预览 (前200字符):', markdownResponse.data.html.substring(0, 200) + '...');
    console.log('');

    // 5. 性能对比
    console.log('5️⃣ 性能对比测试...');
    
    const startVditor = Date.now();
    await axios.post(`${BASE_URL}/api/vditor/convert`, { content: testMarkdown });
    const vditorTime = Date.now() - startVditor;
    
    const startMarkdown = Date.now();
    await axios.post(`${BASE_URL}/api/markdown/convert`, { content: testMarkdown });
    const markdownTime = Date.now() - startMarkdown;
    
    console.log('⏱️ Vditor转换耗时:', vditorTime + 'ms');
    console.log('⏱️ Markdown-it转换耗时:', markdownTime + 'ms');
    console.log('');

    console.log('🎉 所有测试通过！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('📄 错误详情:', error.response.data);
    }
  }
}

// 检查服务是否运行
async function checkService() {
  try {
    await axios.get(`${BASE_URL}/api/health`, { timeout: 3000 });
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  const isRunning = await checkService();
  
  if (!isRunning) {
    console.log('❌ 服务未运行，请先启动服务:');
    console.log('   cd backend');
    console.log('   npm install');
    console.log('   npm start');
    console.log('');
    console.log('然后在另一个终端运行测试:');
    console.log('   npm test');
    return;
  }

  await testAPI();
}

main();

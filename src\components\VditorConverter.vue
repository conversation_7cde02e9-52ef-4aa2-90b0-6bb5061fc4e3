<template>
  <div class="vditor-converter">
    <div class="converter-header">
      <h2>Vditor HTML转换器</h2>
      <div class="status-indicator">
        <span :class="['status-dot', serviceStatus]"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
    </div>

    <div class="converter-content">
      <!-- 输入区域 -->
      <div class="input-section">
        <div class="section-header">
          <h3>Markdown输入</h3>
          <div class="controls">
            <el-select v-model="convertMethod" placeholder="选择转换方法" size="small">
              <el-option label="Vditor (推荐)" value="vditor" />
              <el-option label="Markdown-it (快速)" value="markdown-it" />
              <el-option label="自动选择" value="auto" />
            </el-select>
            <el-button @click="loadExample" size="small" type="info">加载示例</el-button>
            <el-button @click="clearInput" size="small">清空</el-button>
          </div>
        </div>
        <el-input
          v-model="markdownContent"
          type="textarea"
          :rows="12"
          placeholder="请输入Markdown内容..."
          class="markdown-input"
        />
      </div>

      <!-- 转换按钮 -->
      <div class="convert-section">
        <el-button 
          @click="convertContent" 
          type="primary" 
          :loading="converting"
          :disabled="!markdownContent.trim()"
          size="large"
        >
          {{ converting ? '转换中...' : '转换为HTML' }}
        </el-button>
        
        <div v-if="lastConvertTime" class="convert-info">
          <span>转换耗时: {{ lastConvertTime }}ms</span>
          <span>使用方法: {{ lastMethod }}</span>
        </div>
      </div>

      <!-- 输出区域 -->
      <div class="output-section">
        <div class="section-header">
          <h3>HTML输出</h3>
          <div class="controls">
            <el-button @click="copyHTML" size="small" type="success">复制HTML</el-button>
            <el-button @click="previewHTML" size="small" type="warning">预览效果</el-button>
            <el-button @click="clearOutput" size="small">清空</el-button>
          </div>
        </div>
        
        <el-tabs v-model="outputTab" class="output-tabs">
          <el-tab-pane label="HTML代码" name="code">
            <el-input
              v-model="htmlOutput"
              type="textarea"
              :rows="12"
              readonly
              placeholder="转换后的HTML将显示在这里..."
              class="html-output"
            />
          </el-tab-pane>
          <el-tab-pane label="预览效果" name="preview">
            <div class="html-preview" v-html="htmlOutput"></div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 错误提示 -->
    <el-alert
      v-if="errorMessage"
      :title="errorMessage"
      type="error"
      :closable="true"
      @close="errorMessage = ''"
      class="error-alert"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { vditorService } from '@/utils/vditorService';

// 响应式数据
const markdownContent = ref('');
const htmlOutput = ref('');
const converting = ref(false);
const convertMethod = ref<'vditor' | 'markdown-it' | 'auto'>('auto');
const outputTab = ref('code');
const errorMessage = ref('');
const serviceStatus = ref<'online' | 'offline' | 'checking'>('checking');
const lastConvertTime = ref<number | null>(null);
const lastMethod = ref('');

// 计算属性
const statusText = computed(() => {
  switch (serviceStatus.value) {
    case 'online': return '服务在线';
    case 'offline': return '服务离线';
    case 'checking': return '检查中...';
    default: return '未知状态';
  }
});

// 示例Markdown内容
const exampleMarkdown = `# Vditor转换器示例

这是一个**Vditor后端服务**的使用示例。

## 功能特性

- ✅ 支持所有Markdown语法
- ✅ 数学公式渲染
- ✅ 代码高亮
- ✅ 表格支持

## 代码示例

\`\`\`javascript
function convertMarkdown(content) {
  return vditorService.convert(content);
}
\`\`\`

## 数学公式

行内公式：$E = mc^2$

块级公式：
$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$

## 表格

| 方法 | 速度 | 兼容性 |
|------|------|--------|
| Vditor | 慢 | 100% |
| Markdown-it | 快 | 80% |

> 这是一个引用块，用于展示引用效果。

[访问GitHub](https://github.com)
`;

// 方法
const checkServiceStatus = async () => {
  try {
    serviceStatus.value = 'checking';
    await vditorService.checkHealth();
    serviceStatus.value = 'online';
  } catch (error) {
    serviceStatus.value = 'offline';
    console.error('服务状态检查失败:', error);
  }
};

const convertContent = async () => {
  if (!markdownContent.value.trim()) {
    ElMessage.warning('请输入Markdown内容');
    return;
  }

  converting.value = true;
  errorMessage.value = '';
  const startTime = Date.now();

  try {
    let result: string;
    
    switch (convertMethod.value) {
      case 'vditor':
        result = await vditorService.convertWithVditor(markdownContent.value);
        lastMethod.value = 'vditor';
        break;
      case 'markdown-it':
        result = await vditorService.convertWithMarkdownIt(markdownContent.value);
        lastMethod.value = 'markdown-it';
        break;
      case 'auto':
      default:
        result = await vditorService.convert(markdownContent.value, true);
        lastMethod.value = 'auto';
        break;
    }

    htmlOutput.value = result;
    lastConvertTime.value = Date.now() - startTime;
    
    ElMessage.success(`转换成功！耗时 ${lastConvertTime.value}ms`);
    
    // 自动切换到预览标签
    if (outputTab.value === 'code' && result) {
      outputTab.value = 'preview';
    }
    
  } catch (error: any) {
    errorMessage.value = error.message || '转换失败';
    ElMessage.error('转换失败: ' + error.message);
  } finally {
    converting.value = false;
  }
};

const loadExample = () => {
  markdownContent.value = exampleMarkdown;
  ElMessage.info('已加载示例内容');
};

const clearInput = () => {
  markdownContent.value = '';
  ElMessage.info('已清空输入');
};

const clearOutput = () => {
  htmlOutput.value = '';
  lastConvertTime.value = null;
  lastMethod.value = '';
  ElMessage.info('已清空输出');
};

const copyHTML = async () => {
  if (!htmlOutput.value) {
    ElMessage.warning('没有可复制的HTML内容');
    return;
  }

  try {
    await navigator.clipboard.writeText(htmlOutput.value);
    ElMessage.success('HTML已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

const previewHTML = () => {
  if (!htmlOutput.value) {
    ElMessage.warning('没有可预览的HTML内容');
    return;
  }
  outputTab.value = 'preview';
};

// 生命周期
onMounted(() => {
  checkServiceStatus();
});
</script>

<style scoped>
.vditor-converter {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.converter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.converter-header h2 {
  margin: 0;
  color: #303133;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.online {
  background-color: #67c23a;
}

.status-dot.offline {
  background-color: #f56c6c;
}

.status-dot.checking {
  background-color: #e6a23c;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: 14px;
  color: #606266;
}

.converter-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.input-section, .output-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.controls {
  display: flex;
  gap: 8px;
}

.convert-section {
  grid-column: 1 / -1;
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.convert-info {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.markdown-input, .html-output {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.output-tabs {
  margin-top: 12px;
}

.html-preview {
  min-height: 300px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
  overflow-y: auto;
}

.error-alert {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .converter-content {
    grid-template-columns: 1fr;
  }
  
  .converter-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .section-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .controls {
    flex-wrap: wrap;
  }
}
</style>

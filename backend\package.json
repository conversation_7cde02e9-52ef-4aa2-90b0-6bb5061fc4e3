{"name": "vditor-backend-service", "version": "1.0.0", "description": "Node.js后端服务，封装vditor.getHTML()方法", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-deps": "npm install", "test": "node test.js"}, "keywords": ["vditor", "markdown", "html", "converter", "nodejs", "express"], "author": "wbj", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "puppeteer": "^21.5.0", "markdown-it": "^14.1.0", "@iktakahiro/markdown-it-katex": "^4.0.1", "highlight.js": "^11.11.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}
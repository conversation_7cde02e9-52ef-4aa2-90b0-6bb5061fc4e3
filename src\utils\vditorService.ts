/**
 * Vditor后端服务调用工具
 * 封装vditor.getHTML()方法的后端调用
 */

interface VditorConvertOptions {
  mode?: 'ir' | 'wysiwyg' | 'sv';
  height?: number;
  theme?: string;
}

interface ConvertResponse {
  success: boolean;
  html: string;
  method: string;
  error?: string;
  details?: string;
}

interface HealthResponse {
  status: string;
  timestamp: string;
  browser: string;
}

interface MethodInfo {
  name: string;
  endpoint: string;
  description: string;
}

interface MethodsResponse {
  methods: MethodInfo[];
}

class VditorBackendService {
  private baseURL: string;
  private timeout: number;

  constructor(baseURL: string = 'http://localhost:3001', timeout: number = 30000) {
    this.baseURL = baseURL.replace(/\/$/, ''); // 移除末尾斜杠
    this.timeout = timeout;
  }

  /**
   * 使用vditor引擎转换Markdown为HTML
   * @param content Markdown内容
   * @param options vditor配置选项
   * @returns Promise<string> HTML内容
   */
  async convertWithVditor(content: string, options?: VditorConvertOptions): Promise<string> {
    if (!content || content.trim() === '') {
      throw new Error('内容不能为空');
    }

    try {
      const response = await this.fetchWithTimeout(`${this.baseURL}/api/vditor/convert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          options: {
            mode: 'ir',
            height: 300,
            ...options
          }
        })
      });

      const result: ConvertResponse = await response.json();

      if (!result.success) {
        throw new Error(result.error || '转换失败');
      }

      return result.html;
    } catch (error) {
      console.error('Vditor转换失败:', error);
      throw error;
    }
  }

  /**
   * 使用markdown-it引擎转换Markdown为HTML（轻量级方案）
   * @param content Markdown内容
   * @returns Promise<string> HTML内容
   */
  async convertWithMarkdownIt(content: string): Promise<string> {
    if (!content || content.trim() === '') {
      throw new Error('内容不能为空');
    }

    try {
      const response = await this.fetchWithTimeout(`${this.baseURL}/api/markdown/convert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content })
      });

      const result: ConvertResponse = await response.json();

      if (!result.success) {
        throw new Error(result.error || '转换失败');
      }

      return result.html;
    } catch (error) {
      console.error('Markdown-it转换失败:', error);
      throw error;
    }
  }

  /**
   * 自动选择最佳转换方法
   * @param content Markdown内容
   * @param preferVditor 是否优先使用vditor（默认true）
   * @param options vditor配置选项
   * @returns Promise<string> HTML内容
   */
  async convert(content: string, preferVditor: boolean = true, options?: VditorConvertOptions): Promise<string> {
    if (preferVditor) {
      try {
        return await this.convertWithVditor(content, options);
      } catch (error) {
        console.warn('Vditor转换失败，回退到markdown-it:', error);
        return await this.convertWithMarkdownIt(content);
      }
    } else {
      return await this.convertWithMarkdownIt(content);
    }
  }

  /**
   * 检查服务健康状态
   * @returns Promise<HealthResponse>
   */
  async checkHealth(): Promise<HealthResponse> {
    try {
      const response = await this.fetchWithTimeout(`${this.baseURL}/api/health`);
      return await response.json();
    } catch (error) {
      console.error('健康检查失败:', error);
      throw error;
    }
  }

  /**
   * 获取支持的转换方法
   * @returns Promise<MethodsResponse>
   */
  async getSupportedMethods(): Promise<MethodsResponse> {
    try {
      const response = await this.fetchWithTimeout(`${this.baseURL}/api/methods`);
      return await response.json();
    } catch (error) {
      console.error('获取方法列表失败:', error);
      throw error;
    }
  }

  /**
   * 批量转换多个Markdown内容
   * @param contents Markdown内容数组
   * @param preferVditor 是否优先使用vditor
   * @param options vditor配置选项
   * @returns Promise<string[]> HTML内容数组
   */
  async batchConvert(
    contents: string[], 
    preferVditor: boolean = true, 
    options?: VditorConvertOptions
  ): Promise<string[]> {
    const promises = contents.map(content => 
      this.convert(content, preferVditor, options)
    );
    
    return await Promise.all(promises);
  }

  /**
   * 带超时的fetch请求
   * @param url 请求URL
   * @param options fetch选项
   * @returns Promise<Response>
   */
  private async fetchWithTimeout(url: string, options?: RequestInit): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 设置基础URL
   * @param url 新的基础URL
   */
  setBaseURL(url: string): void {
    this.baseURL = url.replace(/\/$/, '');
  }

  /**
   * 设置请求超时时间
   * @param timeout 超时时间（毫秒）
   */
  setTimeout(timeout: number): void {
    this.timeout = timeout;
  }
}

// 创建默认实例
const vditorService = new VditorBackendService();

// 导出类和默认实例
export { VditorBackendService, vditorService };
export type { VditorConvertOptions, ConvertResponse, HealthResponse, MethodsResponse };
